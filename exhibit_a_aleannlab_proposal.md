# Exhibit A: Convx Proposal May 27, 2025

# Proposal: Restaurant Data Platform Frontend Development

## 1 Architecture

### 1.1 System overview

The React single page app is served from S3 through CloudFront. The app talks to FastAPI that runs in AWS Lambda behind API Gateway. FastAPI keeps operational data in Amazon Aurora PostgreSQL Serverless and uses a Redis cluster for sessions and short‑lived caches. Authentication and authorisation are handled by Cognito. Infrastructure provisioning and CI/CD are provided by the CONVX DevOps team; no DevOps work is included in this proposal.

FastAPI also calls the CONVX Data API that sits in front of the Snowflake warehouse managed entirely by CONVX. Our stack never connects directly to Snowflake.

### 1.2 Component layout

- Browser → CloudFront → S3 static site
- Browser → API Gateway → Lambda (FastAPI) → RDS PostgreSQL, Redis, Cognito
- Lambda (FastAPI) → HTTPS → CONVX Data API
- Prefect Cloud handles data pipelines for the warehouse (owned by CONVX)

### 1.3 Scalability and resilience

- Lambda scales on demand.
- Aurora PostgreSQL Serverless v2 scales read and write capacity inside each AWS Region.
- Redis cluster runs Multi‑AZ with automatic failover.
- Multi‑Region delivery is achieved with CloudFront and AWS Global Accelerator.

## 2 Technology justification

- pnpm gives reproducible mono‑repo installs and higher performance than yarn.
- shadcn/ui builds on Tailwind and provides unopinionated accessible components that match the supplied Figma files.
- Vite offers fast incremental builds and hot module reload.
- Aurora PostgreSQL fits operational workloads and scales without manual intervention.
- Keeping application state in Postgres isolates operational traffic from the analytical Snowflake workloads owned by CONVX.

## 3 Application data management

### 3.1 Core domain data

The app stores companies, users, roles, feature toggles and configuration in Postgres. Schemas follow third normal form. Partitioning by organisation_id keeps the path open for future multi‑tenancy.

### 3.2 User profiles

Cognito holds credentials and MFA state. An extended `user_profile` table in Postgres stores time zone, notification preferences and roles. FastAPI joins both at login by the Cognito `sub` claim.

### 3.3 Runtime configuration and feature flags

Feature flags are stored in Postgres and cached in Redis. AWS AppConfig can override any flag without redeploying.

### 3.4 Secrets and environment variables

AWS Secrets Manager holds database credentials, Redis endpoints and JWT signing keys. Rotation runs every thirty days.

### 3.5 Compliance and retention

Personal data is limited to Cognito and user profile tables. Audit logs flow from CloudWatch to S3 with a seven‑year retention policy to support SOC 2 and GDPR.

## 4 API architecture

### 4.1 Authentication and security

JWT bearer tokens issued by Cognito are required for every request under `/v1`. Two roles: `DataAdmin` and `BusinessAdmin`.

### 4.2 Resources and endpoints

(see table in document)

### 4.3 Common response envelope

Successful responses use `{ data, meta }`. Meta carries pagination and rate‑limit info.

### 4.4 Error model

Errors return `{ error: { code, message, details? } }` with status codes 400‑500.

### 4.5 Documentation

OpenAPI 3.1 spec is generated and published at `/docs`.

## 5 Security implementation

- TLS 1.3 end to end
- AWS WAF managed rules
- KMS encryption
- GitHub Actions static analysis
- Signed commits
- SAML SSO via Cognito

## 6 Testing strategy

- Unit: Jest, pytest
- Integration: React Testing Library, MSW, pytest‑asyncio
- E2E: Playwright
- Coverage gate: 80 %

## 7 Project team

| Role | Weekly availability | Hourly rate (USD) |
| --- | --- | --- |
| Lead full‑stack developer | 40 h | 35 |
| Full‑stack developer | 40 h | 35 |
| Full‑stack developer | 40 h | 35 |
| QA engineer | 30 h  | — |
| Project manager | 20 h | — |

*QA and PM costed as 10 % + 10 % of developer spend.*

## 8 Feature scope and effort estimate

| Feature | API endpoints | FE deliverables | FE h | BE h |
| --- | --- | --- | --- | --- |
| User management | `/v1/companies`, `/v1/users`, `/v1/invitations` | Sign‑up, company wizard, user list & invite modal, profile settings | 120 | 80 |
| Integration wizard | `/v1/integration-types`, `/v1/integrations`, `/v1/integrations/{id}/test` | Integration catalog, wizard, credential form, test dialog | 160 | 120 |
| Master data mgmt | `/v1/menu_items`, `/v1/locations`, `/v1/reporting_periods`, `/v1/mappings` | Data tables, mapping dialog, bulk edit UI | 140 | 100 |
| Admin dashboard | `/v1/metrics/admin-dashboard` | Dashboard grid, metric cards, charts | 80 | 60 |
| Alert centre | `/v1/alerts`, `/v1/alerts/{id}/ack` | Alert list, push notifications, preferences | 100 | 80 |
| API & tests | Entire `/v1` | FastAPI routers, models, pytest & Playwright, CI | — | 80 |
| Performance dashboard | `/v1/metrics`, `/v1/forecasts` (TBD) | KPI dashboard, forecast overlays | 160 | 80 |
| AI chat | `/v1/ai/chat` | Streaming chat panel, suggestions | 120 | 120 |
| Alert analysis view | `/v1/alerts/{id}/analysis` (TBD) | Root‑cause panel, factor charts | 100 | 80 |
| Report generator | `/v1/reports`, `/v1/reports/{id}/file` | Report wizard, PDF preview, distribution | 100 | 120 |
| GraphQL facade | `/graphql` | Schema, resolvers, auth middleware | — | 80 |

**Totals**: FE 1 080 h, BE 1 000 h, QA 230 h

## 9 Timeline and milestones

| Phase | Duration | Milestones |
| --- | --- | --- |
| Discovery & setup | 2 w | Architecture confirmed, repo ready |
| Phase 1 impl | 10 w | Admin portal complete, demo |
| Phase 1 launch | 2 w | UAT sign‑off, production launch |
| Phase 2 discovery | 1 w | Scope locked |
| Phase 2 impl | 8 w | Business portal staging |
| Phase 2 launch | 2 w | Production launch |

Total length: **25 weeks**

## 10 Commercials

### 10.1 Payment terms

Time & materials, billed monthly.

Developer rate: USD 35 / h

QA: 10 % of developer spend

PM: 10 % of developer spend

### 10.2 Cost estimate

| Item | Basis | Cost |
| --- | --- | --- |
| Developers | 2 080 h × 35 | 72 800 |
| QA (10 %) | 10 % of dev cost | 7 280 |
| PM (10 %) | 10 % of dev cost | 7 280 |
| **Total** |  | **87 360** |

## 11 Ongoing maintenance

- **Standard**: 40 h/mo bug fixes & minor updates

CONVX handles infrastructure operations (DevOps).