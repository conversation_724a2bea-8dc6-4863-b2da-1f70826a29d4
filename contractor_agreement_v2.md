# SOFTWARE DEVELOPMENT CONTRACTOR AGREEMENT

**Between:** CONVX LLC, a Colorado limited liability company with primary operations in Florida ("Company" or "CONVX")  
**And:** ALEANN LAB ("Contractor")

**Effective Date:** [Date]  
**Project:** Restaurant Data Platform Frontend Development

---

## 1. SCOPE OF WORK

### 1.1 Project Description
Contractor shall provide software development services for CONVX's Restaurant Data Platform, including:

- **Frontend Development:** React single-page application with shadcn/ui components
- **Backend Development:** FastAPI application with AWS Lambda deployment
- **Database Integration:** Aurora PostgreSQL Serverless and Redis implementation
- **Authentication:** AWS Cognito integration
- **Testing:** Unit, integration, and end-to-end testing with 80% coverage requirement

### 1.2 Deliverables
As detailed in the attached proposal dated May 27, 2025, including:

- User management system with company wizard and invitation flows
- Integration wizard for third-party data connections
- Master data management interfaces
- Admin and performance dashboards
- Alert center with notification system
- AI chat functionality
- Report generation system
- Complete API documentation and testing suite

### 1.3 Timeline
- **Total Duration:** 25 weeks from project commencement
- **Phase 1:** 14 weeks (Discovery, Implementation, Launch)
- **Phase 2:** 11 weeks (Discovery, Implementation, Launch)

## 2. TEAM AND RESOURCES

### 2.1 Contractor Team
- **Lead Full-Stack Developer:** 40 hours/week (initial expectation)
- **Full-Stack Developers (2):** 40 hours/week each (initial expectation)
- **QA Engineer:** 30 hours/week (initial expectation)
- **Project Manager:** 20 hours/week (initial expectation)

**Note:** CONVX reserves the right to direct the pace of work and adjust weekly hour commitments based on project needs, priorities, and milestone requirements.

### 2.2 Qualifications
All team members must have:
- Minimum 3 years experience in relevant technologies
- Proven experience with React, TypeScript, and AWS services
- English proficiency sufficient for technical communication
- Availability during overlapping business hours (minimum 4 hours with US Eastern Time)
- Experience with Atlassian Jira and Confluence for project management

## 3. COMPENSATION AND PAYMENT TERMS

### 3.1 Rates and Billing
- **Developer Rate:** $35.00 USD per hour
- **QA Services:** 10% of total developer costs
- **Project Management:** 10% of total developer costs
- **Estimated Total:** $87,360 USD

### 3.2 Payment Schedule
- **Billing:** Monthly invoicing for actual hours worked
- **Payment Terms:** Net 30 days from invoice receipt
- **Invoice Requirements:** Detailed time logs, deliverable status, and progress reports

### 3.3 Expenses
Contractor responsible for all expenses unless pre-approved in writing by CONVX.

## 4. INTELLECTUAL PROPERTY RIGHTS

### 4.1 Work Product Ownership
All work product, including but not limited to:
- Source code, documentation, and technical specifications
- Design assets, algorithms, and data models
- Testing suites and automation scripts
- Any derivative works or improvements

**SHALL BE THE EXCLUSIVE PROPERTY OF CONVX LLC** upon creation.

### 4.2 Assignment of Rights
Contractor hereby assigns, transfers, and conveys to CONVX all rights, title, and interest in and to the work product, including all intellectual property rights worldwide.

### 4.3 Pre-existing IP
Contractor retains rights to pre-existing intellectual property but grants CONVX a perpetual, irrevocable, royalty-free license to use such IP as incorporated into the work product.

### 4.4 Third-Party Components
Contractor must:
- Obtain proper licenses for all third-party components
- Provide CONVX with complete license documentation
- Ensure compatibility with commercial use

## 5. COMPENSATION AND PAYMENT TERMS

### 5.1 Rates and Billing
- **Developer Rate:** $35.00 USD per hour
- **QA Services:** 10% of total developer costs
- **Project Management:** 10% of total developer costs
- **Estimated Total:** $87,360 USD

### 5.2 Payment Schedule
- **Billing:** Monthly invoicing for actual hours worked
- **Payment Terms:** Net 15 days from invoice receipt
- **Invoice Requirements:** Detailed time logs, deliverable status, and progress reports

### 5.3 Expenses
Contractor responsible for all expenses unless pre-approved in writing by CONVX.

## 6. INTELLECTUAL PROPERTY RIGHTS

### 6.1 Work Product Ownership
All work product, including but not limited to:
- Source code, documentation, and technical specifications
- Design assets, algorithms, and data models
- Testing suites and automation scripts
- Any derivative works or improvements

**SHALL BE THE EXCLUSIVE PROPERTY OF CONVX LLC** upon creation.

### 6.2 Assignment of Rights
Contractor hereby assigns, transfers, and conveys to CONVX all rights, title, and interest in and to the work product, including all intellectual property rights worldwide.

### 6.3 Pre-existing IP
Contractor retains rights to pre-existing intellectual property but grants CONVX a perpetual, irrevocable, royalty-free license to use such IP as incorporated into the work product.

### 6.4 Third-Party Components
Contractor must:
- Obtain proper licenses for all third-party components
- Provide CONVX with complete license documentation
- Ensure compatibility with commercial use

## 7. QUALITY ASSURANCE AND STANDARDS

### 5.1 Test Automation Requirements
Contractor must implement comprehensive test automation throughout the development process:

- **Continuous Integration:** All code commits trigger automated test suites
- **Test Types Required:**
  - Unit tests using Jest (frontend) and pytest (backend)
  - Integration tests using React Testing Library, MSW (Mock Service Worker), and pytest-asyncio
  - End-to-end tests using Playwright
  - API contract testing for all endpoints
- **Additional Testing:** Where not specified in contractor's proposal, testing approach shall be mutually agreed upon between CONVX and Contractor before implementation
- **Automated Test Pipeline:** Tests must run automatically on:
  - Every pull request before code review
  - Main branch merges
  - Scheduled nightly builds
  - Pre-deployment validation
- **Test Failure Protocol:** No code deployment allowed with failing tests
- **Performance Testing:** Automated performance benchmarks for API response times
- **Security Testing:** Automated vulnerability scanning integrated into CI/CD pipeline

### 5.2 Quality Requirements
- **Code Coverage:** Minimum 80% test coverage across all modules
- **Documentation:** Complete API documentation using OpenAPI 3.1
- **Code Review:** All code subject to peer review before submission
- **Security:** Compliance with OWASP Top 10 security standards

### 5.3 Acceptance Criteria
Deliverables must:
- Meet functional requirements as specified
- Pass all automated testing suites
- Demonstrate performance benchmarks as specified

### 5.4 Bug Fixes and Warranty
- **Warranty Period:** 90 days post-delivery for each milestone
- **Bug Classification:** Critical, high, medium, low priority system
- **Response Times:** Critical (4 hours), High (24 hours), Medium (72 hours)

## 8. CONFIDENTIALITY AND DATA PROTECTION

### 8.1 Confidential Information
Contractor acknowledges access to CONVX confidential information including:
- Business processes and strategies
- Customer data and analytics
- Proprietary algorithms and methodologies
- Financial information and projections

### 8.2 Non-Disclosure Obligations
Contractor shall:
- Maintain strict confidentiality of all CONVX information
- Not disclose confidential information to third parties
- Use confidential information solely for project purposes
- Return or destroy confidential information upon project completion

### 8.3 Data Security
- **Compliance:** GDPR, SOC 2, and applicable data protection laws
- **Access Controls:** Implement appropriate technical and organizational measures
- **Breach Notification:** Immediate notification of any security incidents

## 9. INDEPENDENT CONTRACTOR RELATIONSHIP

### 9.1 Contractor Status
Contractor is an independent contractor, not an employee of CONVX. Contractor shall:
- Be responsible for all taxes and social contributions
- Maintain appropriate business insurance
- Comply with Ukrainian labor and tax laws

### 9.2 No Authority
Contractor has no authority to bind CONVX or make commitments on CONVX's behalf.

## 10. AGILE PROJECT MANAGEMENT AND ATLASSIAN TOOLS

### 8.1 CONVX-Owned Project Infrastructure
All project management shall be conducted using CONVX's existing Atlassian subscriptions:

- **Jira Project:** Contractor works within CONVX-owned Jira instance and project
- **Confluence Space:** All documentation maintained in CONVX-owned Confluence space
- **Full CONVX Ownership:** CONVX retains complete control and ownership of all project data
- **User Access:** CONVX provides Contractor team with appropriate Jira/Confluence access
- **Data Sovereignty:** All project artifacts remain within CONVX's Atlassian environment

### 8.2 MVP-Optimized Agile Framework
Streamlined Agile approach optimized for MVP development velocity:

- **Sprint Duration:** 1-week sprints for rapid iteration and feedback
- **Simplified Ceremonies:** 
  - Daily async check-ins (no formal standups required)
  - Weekly sprint planning (30 minutes maximum)
  - Weekly sprint reviews with demos
  - Bi-weekly retrospectives (focused on blockers and improvements)
- **User Stories:** Simple story format with clear acceptance criteria
- **Story Points:** Optional - focus on story completion over point estimation

### 8.3 Contractor PM Responsibilities
The Contractor's Project Manager must:

- **Sprint Execution:** Manage sprint goals and track completion within CONVX Jira
- **Backlog Management:** Collaborate with CONVX on prioritization and refinement
- **Status Updates:** Real-time Jira updates and weekly summary reports
- **Blocker Resolution:** Immediate escalation of impediments to CONVX stakeholders
- **Team Coordination:** Ensure development team stays aligned and productive
- **Documentation:** Maintain up-to-date technical docs in CONVX Confluence

### 8.4 High-Velocity MVP Practices
- **Feature Flags:** Build features behind toggles for rapid testing and rollback
- **Minimal Viable Features:** Focus on core functionality over polish
- **Rapid Prototyping:** Quick mockups and proof-of-concepts for validation
- **Continuous Deployment:** Deploy to staging multiple times per week
- **User Feedback Integration:** Direct feedback loops for feature validation

### 8.5 CONVX Visibility and Control
CONVX maintains:

- **Full Administrative Access:** Complete control over Jira project and Confluence space
- **Real-Time Transparency:** Live view of all work items, progress, and blockers
- **Direct Team Communication:** Access to development team for immediate feedback via Slack
- **Backlog Ownership:** CONVX stakeholders control feature prioritization
- **Work Pace Direction:** CONVX can adjust team hours and sprint capacity based on business needs
- **Quality Gates:** CONVX approval required for production deployments

### 8.6 Streamlined Quality Assurance
- **Essential Testing Only:** Focus on automated tests for core user journeys
- **Simplified Documentation:** Keep docs lean and focused on essentials
- **Rapid Feedback Cycles:** Quick validation over extensive documentation
- **Technical Debt Tracking:** Simple backlog items for future improvement
- **Bug Triage:** Weekly review with immediate fixes for critical issues

## 11. PERFORMANCE AND MONITORING

### 11.1 MVP-Focused Performance Metrics
- **Story Completion Rate:** Percentage of committed stories delivered per sprint
- **Time to Production:** Average days from story start to production deployment
- **Bug Resolution Time:** Critical bugs resolved within 24 hours, others within sprint
- **Feature Adoption:** User engagement metrics for delivered features
- **Development Velocity:** Stories completed per week trending upward

### 11.2 Streamlined Reporting
- **Daily Updates:** Real-time Jira status updates (no formal reports required)
- **Weekly Sprint Reviews:** Brief demos and next week planning (30 minutes max)
- **Monthly Check-ins:** Progress assessment and course correction sessions
- **Ad-hoc Communication:** Slack for immediate questions and blockers

### 11.3 CONVX-Controlled Project Tools
- **Primary Platform:** CONVX's Atlassian Jira for all project tracking
- **Documentation:** CONVX's Confluence for technical documentation
- **Communication:** CONVX-designated Slack channels
- **Code Repository:** Integration with CONVX's GitHub repository
- **Time Tracking:** Simplified time logging in CONVX Jira for billing accuracy

## 12. TERMINATION

### 12.1 Termination for Convenience
Either party may terminate with 30 days written notice.

### 12.2 Termination for Cause
Either party may terminate immediately for:
- Material breach of contract terms
- Failure to meet quality standards after notice and cure period
- Violation of confidentiality or security requirements
- Failure to maintain agreed story completion rates or delivery commitments

### 12.3 Effects of Termination
Upon termination:
- Contractor shall deliver all work product and confidential information
- CONVX retains all project data in its Atlassian environment (no transfer required)
- Contractor access to CONVX Jira and Confluence will be revoked
- CONVX shall pay for satisfactory work completed through termination date
- All confidentiality and IP assignment obligations survive termination

## 13. LIABILITY AND INDEMNIFICATION

### 13.1 Limitation of Liability
Neither party shall be liable for indirect, incidental, or consequential damages. Total liability shall not exceed the total contract value.

### 13.2 Indemnification
Contractor shall indemnify CONVX against:
- IP infringement claims related to contractor's work
- Data breaches caused by contractor's negligence
- Violations of applicable laws or regulations

## 14. DISPUTE RESOLUTION

### 14.1 Governing Law
This agreement shall be governed by Florida law, without regard to conflict of law principles.

### 14.2 Dispute Resolution Process
1. **Direct Negotiation:** 30-day good faith negotiation period
2. **Mediation:** Binding mediation if negotiation fails
3. **Arbitration:** Final dispute resolution through Florida arbitration

### 14.3 Jurisdiction
Any legal proceedings shall be conducted in the state or federal courts located in Orlando, Florida for the convenience of CONVX executives.

## 15. GENERAL PROVISIONS

### 15.1 Entire Agreement
This agreement, including the attached proposal, constitutes the entire agreement between the parties.

### 15.2 Amendments
All modifications must be in writing and signed by both parties.

### 15.3 Severability
If any provision is unenforceable, the remainder of the agreement remains in effect.

### 15.4 Force Majeure
Neither party shall be liable for delays caused by circumstances beyond reasonable control, including acts of war, natural disasters, or government actions.

### 15.5 Notices
All notices must be in writing and delivered to the addresses specified below.

---

## SIGNATURES

**CONVX LLC**

By: _________________________________  
Name: [Name]  
Title: [Title]  
Date: _______________

**ALEANN LAB**

By: _________________________________  
Name: [Name]  
Title: [Title]  
Date: _______________

---

**ATTACHMENTS:**
- Exhibit A: Project Proposal dated May 27, 2025
- Exhibit B: Technical Specifications and Requirements
- Exhibit C: Security and Compliance Requirements